import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  <PERSON><PERSON><PERSON>, 
  Users, 
  UserCheck, 
  FileText, 
  Plus, 
  TrendingUp,
  Clock,
  Eye,
  LucideIcon
} from 'lucide-react';
import api from '../../utils/api';
import toast from 'react-hot-toast';

interface Stats {
  total_materials: number;
  total_students: number;
  total_assignments: number;
  total_exercises: number;
}

interface Material {
  id: string;
  title: string;
  created_by_name: string;
  created_at: string;
}

interface StatCard {
  name: string;
  value: number;
  icon: LucideIcon;
  color: string;
  bgColor: string;
  textColor: string;
}

const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<Stats>({
    total_materials: 0,
    total_students: 0,
    total_assignments: 0,
    total_exercises: 0
  });
  const [recentMaterials, setRecentMaterials] = useState<Material[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async (): Promise<void> => {
    try {
      const response = await api.get('/admin/dashboard/stats');
      if (response.data.success) {
        setStats(response.data.data.stats);
        setRecentMaterials(response.data.data.recent_materials);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const statCards: StatCard[] = [
    {
      name: 'Learning Materials',
      value: stats.total_materials,
      icon: BookOpen,
      color: 'bg-blue-500',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600'
    },
    {
      name: 'Total Students',
      value: stats.total_students,
      icon: Users,
      color: 'bg-green-500',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600'
    },
    {
      name: 'Assignments',
      value: stats.total_assignments,
      icon: UserCheck,
      color: 'bg-yellow-500',
      bgColor: 'bg-yellow-50',
      textColor: 'text-yellow-600'
    },
    {
      name: 'Practice Exercises',
      value: stats.total_exercises,
      icon: FileText,
      color: 'bg-purple-500',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-600'
    }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-poker-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600">Manage learning materials and track student progress</p>
        </div>
        <Link
          to="/admin/learning-materials/create"
          className="btn-primary flex items-center space-x-2"
        >
          <Plus className="h-5 w-5" />
          <span>Create Material</span>
        </Link>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="card p-6">
              <div className="flex items-center">
                <div className={`h-12 w-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                  <Icon className={`h-6 w-6 ${stat.textColor}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-semibold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link
            to="/admin/learning-materials/create"
            className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-poker-400 hover:bg-poker-50 transition-colors group"
          >
            <div className="text-center">
              <Plus className="h-8 w-8 text-gray-400 group-hover:text-poker-500 mx-auto mb-2" />
              <span className="text-sm font-medium text-gray-600 group-hover:text-poker-600">
                Create Material
              </span>
            </div>
          </Link>
          
          <Link
            to="/admin/learning-materials"
            className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors group"
          >
            <div className="text-center">
              <BookOpen className="h-8 w-8 text-gray-400 group-hover:text-blue-500 mx-auto mb-2" />
              <span className="text-sm font-medium text-gray-600 group-hover:text-blue-600">
                Manage Materials
              </span>
            </div>
          </Link>
          
          <Link
            to="/admin/students"
            className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-400 hover:bg-green-50 transition-colors group"
          >
            <div className="text-center">
              <Users className="h-8 w-8 text-gray-400 group-hover:text-green-500 mx-auto mb-2" />
              <span className="text-sm font-medium text-gray-600 group-hover:text-green-600">
                View Students
              </span>
            </div>
          </Link>
          
          <Link
            to="/admin/analytics"
            className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-colors group"
          >
            <div className="text-center">
              <TrendingUp className="h-8 w-8 text-gray-400 group-hover:text-purple-500 mx-auto mb-2" />
              <span className="text-sm font-medium text-gray-600 group-hover:text-purple-600">
                Analytics
              </span>
            </div>
          </Link>
        </div>
      </div>

      {/* Recent Materials */}
      <div className="card p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Recent Learning Materials</h3>
          <Link
            to="/admin/learning-materials"
            className="text-sm text-poker-600 hover:text-poker-700 font-medium"
          >
            View all
          </Link>
        </div>
        
        {recentMaterials.length > 0 ? (
          <div className="space-y-3">
            {recentMaterials.map((material) => (
              <div key={material.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 bg-poker-100 rounded-lg flex items-center justify-center">
                    <BookOpen className="h-5 w-5 text-poker-600" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">{material.title}</h4>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <span>Created by {material.created_by_name}</span>
                      <span>•</span>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{new Date(material.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <Link
                  to={`/admin/learning-materials/${material.id}`}
                  className="flex items-center space-x-1 text-sm text-poker-600 hover:text-poker-700"
                >
                  <Eye className="h-4 w-4" />
                  <span>View</span>
                </Link>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No learning materials created yet</p>
            <Link
              to="/admin/learning-materials/create"
              className="mt-2 inline-flex items-center text-sm text-poker-600 hover:text-poker-700"
            >
              <Plus className="h-4 w-4 mr-1" />
              Create your first material
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;
