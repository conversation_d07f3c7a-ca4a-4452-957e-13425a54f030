import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Save, BookOpen } from 'lucide-react';
import api from '../../utils/api';
import toast from 'react-hot-toast';

interface Student {
  id: string;
  name: string;
  email: string;
}

interface FormData {
  title: string;
  description: string;
  assigned_students: string[];
  pre_learning: {
    title: string;
    description: string;
    total_time_estimate: string;
    sections: any[];
  };
  video_lessons: any[];
  key_learning_points: any[];
  practice_exercises: any[];
}

const LearningMaterialCreate: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState<boolean>(false);
  const [students, setStudents] = useState<Student[]>([]);

  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    assigned_students: [],
    pre_learning: {
      title: '',
      description: '',
      total_time_estimate: '',
      sections: []
    },
    video_lessons: [],
    key_learning_points: [],
    practice_exercises: []
  });

  useEffect(() => {
    fetchStudents();
  }, []);

  const fetchStudents = async (): Promise<void> => {
    try {
      const response = await api.get('/admin/students');
      if (response.data.success) {
        setStudents(response.data.data);
      }
    } catch (error) {
      console.error('Failed to fetch students:', error);
      toast.error('Failed to load students');
    }
  };

  const handleBasicChange = (field: keyof FormData, value: string | string[]): void => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      toast.error('Title is required');
      return;
    }

    setLoading(true);
    try {
      const response = await api.post('/admin/learning-materials', formData);
      
      if (response.data.success) {
        toast.success('Learning material created successfully');
        navigate('/admin/learning-materials');
      } else {
        toast.error(response.data.message || 'Failed to create learning material');
      }
    } catch (error: any) {
      console.error('Failed to create learning material:', error);
      
      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
        toast.error('Request timed out. Please check if your material was created and try again.');
      } else if (error.response) {
        const message = error.response.data?.message || 'Failed to create learning material';
        toast.error(message);
      } else if (error.request) {
        toast.error('Network error. Please check if your material was created and try again.');
      } else {
        toast.error('An unexpected error occurred. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/admin/learning-materials')}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create Learning Material</h1>
            <p className="text-gray-600">Build comprehensive learning content for your students</p>
          </div>
        </div>
        <button
          onClick={handleSubmit}
          disabled={loading}
          className="btn-primary flex items-center space-x-2"
        >
          <Save className="h-5 w-5" />
          <span>{loading ? 'Creating...' : 'Create Material'}</span>
        </button>
      </div>

      {/* Basic Form */}
      <div className="card p-6">
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleBasicChange('title', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-poker-500 focus:border-transparent"
              placeholder="Enter material title"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleBasicChange('description', e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-poker-500 focus:border-transparent"
              placeholder="Describe what students will learn"
            />
          </div>

          {/* Students Assignment */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Assign to Students
            </label>
            <div className="space-y-2 max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-3">
              {students.map((student) => (
                <label key={student.id} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.assigned_students.includes(student.id)}
                    onChange={() => {
                      const newAssigned = formData.assigned_students.includes(student.id)
                        ? formData.assigned_students.filter(id => id !== student.id)
                        : [...formData.assigned_students, student.id];
                      handleBasicChange('assigned_students', newAssigned);
                    }}
                    className="h-4 w-4 text-poker-600 focus:ring-poker-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">{student.name} ({student.email})</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LearningMaterialCreate;
