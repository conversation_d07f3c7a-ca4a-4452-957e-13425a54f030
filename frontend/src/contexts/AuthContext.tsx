import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authAPI, setAuthToken, getAuthToken, setUser, getUser, logout, LoginCredentials, RegisterData, User } from '../utils/api';
import toast from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; message?: string }>;
  register: (userData: RegisterData) => Promise<{ success: boolean; message?: string }>;
  logout: () => void;
}

interface AuthProviderProps {
  children: ReactNode;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUserState] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  // Initialize auth state on app load
  useEffect(() => {
    const initializeAuth = async () => {
      const token = getAuthToken();
      const savedUser = getUser();

      if (token && savedUser) {
        try {
          // Verify token is still valid by fetching profile
          const updatedUser = await authAPI.getProfile();
          
          // Update localStorage with fresh user data (including role)
          setUser(updatedUser);
          setUserState(updatedUser);
          setIsAuthenticated(true);
        } catch (error) {
          // Token is invalid, clear storage
          logout();
        }
      }
      
      setLoading(false);
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<{ success: boolean; message?: string }> => {
    try {
      setLoading(true);
      const response = await authAPI.login(credentials);
      
      const { user, token } = response;
      
      // Save to localStorage
      setAuthToken(token);
      setUser(user);
      
      // Update state
      setUserState(user);
      setIsAuthenticated(true);
      
      toast.success('Login successful!');
      return { success: true };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Login failed';
      toast.error(message);
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData: RegisterData): Promise<{ success: boolean; message?: string }> => {
    try {
      setLoading(true);
      const response = await authAPI.register(userData);
      
      const { user, token } = response;
      
      // Save to localStorage
      setAuthToken(token);
      setUser(user);
      
      // Update state
      setUserState(user);
      setIsAuthenticated(true);
      
      toast.success('Registration successful!');
      return { success: true };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Registration failed';
      toast.error(message);
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  };

  const logoutUser = (): void => {
    setUserState(null);
    setIsAuthenticated(false);
    logout();
    toast.success('Logged out successfully');
  };

  const value: AuthContextType = {
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout: logoutUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
