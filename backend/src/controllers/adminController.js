const LearningMaterial = require('../models/LearningMaterial');
const PreLearning = require('../models/PreLearning');
const VideoLesson = require('../models/VideoLesson');
const KeyLearningPoint = require('../models/KeyLearningPoint');
const PracticeExercise = require('../models/PracticeExercise');
const User = require('../models/User');
const pool = require('../config/database');

class AdminController {
  // Get all learning materials with pagination and search
  static async getLearningMaterials(req, res) {
    try {
      const { page = 1, limit = 10, search = '' } = req.query;
      const offset = (page - 1) * limit;

      const materials = await LearningMaterial.findAll({
        limit: parseInt(limit),
        offset: parseInt(offset),
        search: search.trim()
      });

      const totalCount = await LearningMaterial.getCount(search.trim());
      const totalPages = Math.ceil(totalCount / limit);

      res.json({
        success: true,
        data: {
          materials,
          pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalCount,
            hasNext: page < totalPages,
            hasPrev: page > 1
          }
        }
      });
    } catch (error) {
      console.error('Get learning materials error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch learning materials'
      });
    }
  }

  // Get single learning material with all details
  static async getLearningMaterial(req, res) {
    try {
      const { id } = req.params;
      const material = await LearningMaterial.findByIdWithDetails(id);

      if (!material) {
        return res.status(404).json({
          success: false,
          message: 'Learning material not found'
        });
      }

      res.json({
        success: true,
        data: material
      });
    } catch (error) {
      console.error('Get learning material error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch learning material'
      });
    }
  }

  // Create new learning material with all sections
  static async createLearningMaterial(req, res) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');

      const {
        title,
        order_index,
        description,
        assigned_students = [],
        pre_learning,
        video_lessons = [],
        key_learning_points = [],
        practice_exercises = []
      } = req.body;

      // Create main learning material
      const material = await LearningMaterial.create({
        title,
        order_index,
        description,
        created_by: req.user.id
      });

      const materialId = material.id;

      // Create pre learning if provided
      if (pre_learning) {
        await PreLearning.createOrUpdateWithSections(materialId, pre_learning);
      }

      // Create video lessons
      if (video_lessons.length > 0) {
        await VideoLesson.createOrUpdateForMaterial(materialId, video_lessons);
      }

      // Create key learning points
      if (key_learning_points.length > 0) {
        await KeyLearningPoint.createOrUpdateForMaterial(materialId, key_learning_points);
      }

      // Create practice exercises
      if (practice_exercises.length > 0) {
        await PracticeExercise.createOrUpdateForMaterial(materialId, practice_exercises);
      }

      // Assign to students
      if (assigned_students.length > 0) {
        await LearningMaterial.assignStudents(materialId, assigned_students);
      }

      await client.query('COMMIT');

      // Fetch the complete material with all details
      const completeMaterial = await LearningMaterial.findByIdWithDetails(materialId);

      res.status(201).json({
        success: true,
        message: 'Learning material created successfully',
        data: completeMaterial
      });
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Create learning material error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create learning material'
      });
    } finally {
      client.release();
    }
  }

  // Update learning material
  static async updateLearningMaterial(req, res) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');

      const { id } = req.params;
      const {
        title,
        order_index,
        description,
        assigned_students = [],
        pre_learning,
        video_lessons = [],
        key_learning_points = [],
        practice_exercises = []
      } = req.body;

      // Check if material exists
      const existingMaterial = await LearningMaterial.findById(id);
      if (!existingMaterial) {
        return res.status(404).json({
          success: false,
          message: 'Learning material not found'
        });
      }

      // Update main learning material
      await LearningMaterial.update(id, {
        title,
        order_index,
        description
      });

      // Update pre learning - always update if pre_learning object exists
      if (pre_learning) {
        await PreLearning.createOrUpdateWithSections(id, pre_learning);
      }

      // Update video lessons
      await VideoLesson.createOrUpdateForMaterial(id, video_lessons);

      // Update key learning points
      await KeyLearningPoint.createOrUpdateForMaterial(id, key_learning_points);

      // Update practice exercises
      await PracticeExercise.createOrUpdateForMaterial(id, practice_exercises);

      // Update student assignments
      await LearningMaterial.assignStudents(id, assigned_students);

      await client.query('COMMIT');

      // Fetch the updated material with all details
      const updatedMaterial = await LearningMaterial.findByIdWithDetails(id);

      res.json({
        success: true,
        message: 'Learning material updated successfully',
        data: updatedMaterial
      });
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Update learning material error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update learning material'
      });
    } finally {
      client.release();
    }
  }

  // Delete learning material
  static async deleteLearningMaterial(req, res) {
    try {
      const { id } = req.params;

      const material = await LearningMaterial.findById(id);
      if (!material) {
        return res.status(404).json({
          success: false,
          message: 'Learning material not found'
        });
      }

      await LearningMaterial.delete(id);

      res.json({
        success: true,
        message: 'Learning material deleted successfully'
      });
    } catch (error) {
      console.error('Delete learning material error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete learning material'
      });
    }
  }

  // Get all students for assignment
  static async getStudents(req, res) {
    try {
      const query = `
        SELECT id, first_name, last_name, email 
        FROM users 
        WHERE role = 'student' OR role IS NULL
        ORDER BY first_name, last_name
      `;
      
      const result = await pool.query(query);
      
      res.json({
        success: true,
        data: result.rows
      });
    } catch (error) {
      console.error('Get students error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch students'
      });
    }
  }

  // Assign students to learning material
  static async assignStudents(req, res) {
    try {
      const { id } = req.params;
      const { student_ids } = req.body;

      const material = await LearningMaterial.findById(id);
      if (!material) {
        return res.status(404).json({
          success: false,
          message: 'Learning material not found'
        });
      }

      await LearningMaterial.assignStudents(id, student_ids);

      res.json({
        success: true,
        message: 'Students assigned successfully'
      });
    } catch (error) {
      console.error('Assign students error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to assign students'
      });
    }
  }

  // Get admin dashboard statistics
  static async getDashboardStats(req, res) {
    try {
      const stats = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM learning_materials) as total_materials,
          (SELECT COUNT(*) FROM users WHERE role = 'student' OR role IS NULL) as total_students,
          (SELECT COUNT(*) FROM learning_material_students) as total_assignments,
          (SELECT COUNT(*) FROM practice_exercises) as total_exercises
      `);

      // Try to get recent materials with created_by, fallback if column doesn't exist
      let recentMaterials;
      try {
        recentMaterials = await pool.query(`
          SELECT lm.id, lm.title, lm.created_at,
                 u.first_name || ' ' || u.last_name as created_by_name
          FROM learning_materials lm
          LEFT JOIN users u ON lm.created_by = u.id
          ORDER BY lm.created_at DESC
          LIMIT 5
        `);
      } catch (columnError) {
        console.log('created_by column not found, using fallback query');
        // If created_by column doesn't exist, fetch without it
        recentMaterials = await pool.query(`
          SELECT id, title, created_at,
                 'Unknown' as created_by_name
          FROM learning_materials
          ORDER BY created_at DESC
          LIMIT 5
        `);
      }

      res.json({
        success: true,
        data: {
          stats: stats.rows[0],
          recent_materials: recentMaterials.rows
        }
      });
    } catch (error) {
      console.error('Get dashboard stats error:', error);
      
      // If all else fails, return basic stats without recent materials
      try {
        const basicStats = await pool.query(`
          SELECT 
            (SELECT COUNT(*) FROM learning_materials) as total_materials,
            (SELECT COUNT(*) FROM users WHERE role = 'student' OR role IS NULL) as total_students,
            (SELECT COUNT(*) FROM learning_material_students) as total_assignments,
            (SELECT COUNT(*) FROM practice_exercises) as total_exercises
        `);
        
        res.json({
          success: true,
          data: {
            stats: basicStats.rows[0],
            recent_materials: []
          }
        });
      } catch (fallbackError) {
        res.status(500).json({
          success: false,
          message: 'Failed to fetch dashboard statistics'
        });
      }
    }
  }

  // Duplicate learning material
  static async duplicateLearningMaterial(req, res) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');

      const { id } = req.params;
      const originalMaterial = await LearningMaterial.findByIdWithDetails(id);

      if (!originalMaterial) {
        return res.status(404).json({
          success: false,
          message: 'Learning material not found'
        });
      }

      // Create duplicate with modified title
      const duplicateData = {
        title: `${originalMaterial.title} (Copy)`,
        order_index: originalMaterial.order_index + 1,
        description: originalMaterial.description,
        created_by: req.user.id
      };

      const duplicateMaterial = await LearningMaterial.create(duplicateData);
      const duplicateId = duplicateMaterial.id;

      // Duplicate pre learning
      if (originalMaterial.pre_learning) {
        await PreLearning.createOrUpdateWithSections(duplicateId, originalMaterial.pre_learning);
      }

      // Duplicate video lessons
      if (originalMaterial.video_lessons.length > 0) {
        await VideoLesson.createOrUpdateForMaterial(duplicateId, originalMaterial.video_lessons);
      }

      // Duplicate key learning points
      if (originalMaterial.key_learning_points.length > 0) {
        await KeyLearningPoint.createOrUpdateForMaterial(duplicateId, originalMaterial.key_learning_points);
      }

      // Duplicate practice exercises
      if (originalMaterial.practice_exercises.length > 0) {
        await PracticeExercise.createOrUpdateForMaterial(duplicateId, originalMaterial.practice_exercises);
      }

      await client.query('COMMIT');

      const completeDuplicate = await LearningMaterial.findByIdWithDetails(duplicateId);

      res.status(201).json({
        success: true,
        message: 'Learning material duplicated successfully',
        data: completeDuplicate
      });
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Duplicate learning material error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to duplicate learning material'
      });
    } finally {
      client.release();
    }
  }
}

module.exports = AdminController;
