const pool = require('../config/database');

class KeyLearningPoint {
  // Create key learning point
  static async create(pointData) {
    const { learning_material_id, title, description, order_index } = pointData;
    
    const query = `
      INSERT INTO key_learning_points (learning_material_id, title, description, order_index, created_at, updated_at)
      VALUES ($1, $2, $3, $4, NOW(), NOW())
      RETURNING *
    `;
    
    const values = [learning_material_id, title, description, order_index];
    
    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Find key learning points by learning material ID
  static async findByLearningMaterialId(learningMaterialId) {
    const query = `
      SELECT * FROM key_learning_points 
      WHERE learning_material_id = $1 
      ORDER BY order_index ASC, created_at ASC
    `;
    
    try {
      const result = await pool.query(query, [learningMaterialId]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Update key learning point
  static async update(id, pointData) {
    const { title, description, order_index } = pointData;
    
    const query = `
      UPDATE key_learning_points 
      SET title = $1, description = $2, order_index = $3, updated_at = NOW()
      WHERE id = $4
      RETURNING *
    `;
    
    const values = [title, description, order_index, id];
    
    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Delete key learning point
  static async delete(id) {
    const query = 'DELETE FROM key_learning_points WHERE id = $1 RETURNING *';
    
    try {
      const result = await pool.query(query, [id]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Create or update key learning points for a learning material
  static async createOrUpdateForMaterial(learningMaterialId, keyPoints) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Delete existing key learning points
      await client.query('DELETE FROM key_learning_points WHERE learning_material_id = $1', [learningMaterialId]);
      
      const createdPoints = [];
      
      // Create new key learning points
      if (keyPoints && keyPoints.length > 0) {
        for (let i = 0; i < keyPoints.length; i++) {
          const point = keyPoints[i];
          const result = await client.query(`
            INSERT INTO key_learning_points (learning_material_id, title, description, order_index, created_at, updated_at)
            VALUES ($1, $2, $3, $4, NOW(), NOW())
            RETURNING *
          `, [learningMaterialId, point.title, point.description, i + 1]);
          
          createdPoints.push(result.rows[0]);
        }
      }
      
      await client.query('COMMIT');
      return createdPoints;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Delete all key learning points for a learning material
  static async deleteByLearningMaterialId(learningMaterialId) {
    const query = 'DELETE FROM key_learning_points WHERE learning_material_id = $1 RETURNING *';
    
    try {
      const result = await pool.query(query, [learningMaterialId]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Reorder key learning points
  static async reorder(learningMaterialId, orderedIds) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      for (let i = 0; i < orderedIds.length; i++) {
        await client.query(`
          UPDATE key_learning_points 
          SET order_index = $1, updated_at = NOW()
          WHERE id = $2 AND learning_material_id = $3
        `, [i + 1, orderedIds[i], learningMaterialId]);
      }
      
      await client.query('COMMIT');
      return true;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }
}

module.exports = KeyLearningPoint;
