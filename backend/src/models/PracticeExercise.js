const pool = require('../config/database');

class PracticeExercise {
  // Create practice exercise
  static async create(exerciseData) {
    const { 
      learning_material_id, 
      question, 
      answer_options, 
      correct_answer, 
      explanation, 
      order_index 
    } = exerciseData;
    
    const query = `
      INSERT INTO practice_exercises (
        learning_material_id, question, answer_options, correct_answer, 
        explanation, order_index, created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
      RETURNING *
    `;
    
    const values = [
      learning_material_id, 
      question, 
      JSON.stringify(answer_options), 
      correct_answer, 
      explanation, 
      order_index
    ];
    
    try {
      const result = await pool.query(query, values);
      const exercise = result.rows[0];
      // Parse JSON fields
      exercise.answer_options = JSON.parse(exercise.answer_options);
      return exercise;
    } catch (error) {
      throw error;
    }
  }

  // Find practice exercises by learning material ID
  static async findByLearningMaterialId(learningMaterialId) {
    const query = `
      SELECT * FROM practice_exercises 
      WHERE learning_material_id = $1 
      ORDER BY order_index ASC, created_at ASC
    `;
    
    try {
      const result = await pool.query(query, [learningMaterialId]);
      // Parse JSON fields for each exercise
      const exercises = result.rows.map(exercise => ({
        ...exercise,
        answer_options: JSON.parse(exercise.answer_options)
      }));
      return exercises;
    } catch (error) {
      throw error;
    }
  }

  // Update practice exercise
  static async update(id, exerciseData) {
    const { question, answer_options, correct_answer, explanation, order_index } = exerciseData;
    
    const query = `
      UPDATE practice_exercises 
      SET question = $1, answer_options = $2, correct_answer = $3, 
          explanation = $4, order_index = $5, updated_at = NOW()
      WHERE id = $6
      RETURNING *
    `;
    
    const values = [
      question, 
      JSON.stringify(answer_options), 
      correct_answer, 
      explanation, 
      order_index, 
      id
    ];
    
    try {
      const result = await pool.query(query, values);
      const exercise = result.rows[0];
      // Parse JSON fields
      exercise.answer_options = JSON.parse(exercise.answer_options);
      return exercise;
    } catch (error) {
      throw error;
    }
  }

  // Delete practice exercise
  static async delete(id) {
    const query = 'DELETE FROM practice_exercises WHERE id = $1 RETURNING *';
    
    try {
      const result = await pool.query(query, [id]);
      if (result.rows[0]) {
        const exercise = result.rows[0];
        exercise.answer_options = JSON.parse(exercise.answer_options);
        return exercise;
      }
      return null;
    } catch (error) {
      throw error;
    }
  }

  // Create or update practice exercises for a learning material
  static async createOrUpdateForMaterial(learningMaterialId, exercises) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Delete existing practice exercises
      await client.query('DELETE FROM practice_exercises WHERE learning_material_id = $1', [learningMaterialId]);
      
      const createdExercises = [];
      
      // Create new practice exercises
      if (exercises && exercises.length > 0) {
        for (let i = 0; i < exercises.length; i++) {
          const exercise = exercises[i];
          const result = await client.query(`
            INSERT INTO practice_exercises (
              learning_material_id, question, answer_options, correct_answer, 
              explanation, order_index, created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
            RETURNING *
          `, [
            learningMaterialId, 
            exercise.question, 
            JSON.stringify(exercise.answer_options), 
            exercise.correct_answer, 
            exercise.explanation, 
            i + 1
          ]);
          
          const createdExercise = result.rows[0];
          createdExercise.answer_options = JSON.parse(createdExercise.answer_options);
          createdExercises.push(createdExercise);
        }
      }
      
      await client.query('COMMIT');
      return createdExercises;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Delete all practice exercises for a learning material
  static async deleteByLearningMaterialId(learningMaterialId) {
    const query = 'DELETE FROM practice_exercises WHERE learning_material_id = $1 RETURNING *';
    
    try {
      const result = await pool.query(query, [learningMaterialId]);
      // Parse JSON fields for each exercise
      const exercises = result.rows.map(exercise => ({
        ...exercise,
        answer_options: JSON.parse(exercise.answer_options)
      }));
      return exercises;
    } catch (error) {
      throw error;
    }
  }

  // Reorder practice exercises
  static async reorder(learningMaterialId, orderedIds) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      for (let i = 0; i < orderedIds.length; i++) {
        await client.query(`
          UPDATE practice_exercises 
          SET order_index = $1, updated_at = NOW()
          WHERE id = $2 AND learning_material_id = $3
        `, [i + 1, orderedIds[i], learningMaterialId]);
      }
      
      await client.query('COMMIT');
      return true;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Find practice exercise by ID
  static async findById(id) {
    const query = 'SELECT * FROM practice_exercises WHERE id = $1';
    
    try {
      const result = await pool.query(query, [id]);
      if (result.rows[0]) {
        const exercise = result.rows[0];
        exercise.answer_options = JSON.parse(exercise.answer_options);
        return exercise;
      }
      return null;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = PracticeExercise;
