const pool = require('../config/database');

class LearningMaterial {
  // Create a new learning material
  static async create(materialData) {
    const { title, description, created_by } = materialData;
    
    try {
      const query = `
        INSERT INTO learning_materials (title, description, created_by, created_at, updated_at)
        VALUES ($1, $2, $3, NOW(), NOW())
        RETURNING *
      `;
      
      const values = [title, description, created_by];
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      // If created_by column doesn't exist, use fallback query
      if (error.code === '42703') {
        const fallbackQuery = `
          INSERT INTO learning_materials (title, description, created_at, updated_at)
          VALUES ($1, $2, NOW(), NOW())
          RETURNING *
        `;
        
        const fallbackValues = [title, description];
        const result = await pool.query(fallbackQuery, fallbackValues);
        return result.rows[0];
      }
      throw error;
    }
  }

  // Get all learning materials
  static async findAll(options = {}) {
    const { limit, offset, search } = options;
    
    // Try with created_by column first, fallback if it doesn't exist
    let query, values = [], paramCount = 0;
    
    try {
      query = `
        SELECT lm.*, 
               u.first_name || ' ' || u.last_name as created_by_name,
               COUNT(lms.student_id) as assigned_students_count
        FROM learning_materials lm
        LEFT JOIN users u ON lm.created_by = u.id
        LEFT JOIN learning_material_students lms ON lm.id = lms.learning_material_id
      `;
      
      if (search) {
        paramCount++;
        query += ` WHERE lm.title ILIKE $${paramCount}`;
        values.push(`%${search}%`);
      }
      
      query += ` GROUP BY lm.id, u.first_name, u.last_name ORDER BY lm.created_at DESC`;
      
      if (limit) {
        paramCount++;
        query += ` LIMIT $${paramCount}`;
        values.push(limit);
      }
      
      if (offset) {
        paramCount++;
        query += ` OFFSET $${paramCount}`;
        values.push(offset);
      }
      
      const result = await pool.query(query, values);
      return result.rows;
    } catch (error) {
      // If created_by column doesn't exist, use fallback query
      if (error.code === '42703') {
        values = [];
        paramCount = 0;
        
        query = `
          SELECT lm.*, 
                 'Unknown' as created_by_name,
                 COUNT(lms.student_id) as assigned_students_count
          FROM learning_materials lm
          LEFT JOIN learning_material_students lms ON lm.id = lms.learning_material_id
        `;
        
        if (search) {
          paramCount++;
          query += ` WHERE lm.title ILIKE $${paramCount}`;
          values.push(`%${search}%`);
        }
        
        query += ` GROUP BY lm.id ORDER BY lm.created_at DESC`;
        
        if (limit) {
          paramCount++;
          query += ` LIMIT $${paramCount}`;
          values.push(limit);
        }
        
        if (offset) {
          paramCount++;
          query += ` OFFSET $${paramCount}`;
          values.push(offset);
        }
        
        const result = await pool.query(query, values);
        return result.rows;
      }
      throw error;
    }
  }

  // Find learning material by ID with all related data
  static async findById(id) {
    try {
      const query = `
        SELECT lm.*, 
               u.first_name || ' ' || u.last_name as created_by_name
        FROM learning_materials lm
        LEFT JOIN users u ON lm.created_by = u.id
        WHERE lm.id = $1
      `;
      
      const result = await pool.query(query, [id]);
      return result.rows[0];
    } catch (error) {
      // If created_by column doesn't exist, use fallback query
      if (error.code === '42703') {
        const fallbackQuery = `
          SELECT *, 'Unknown' as created_by_name
          FROM learning_materials
          WHERE id = $1
        `;
        const result = await pool.query(fallbackQuery, [id]);
        return result.rows[0];
      }
      throw error;
    }
  }

  // Update learning material
  static async update(id, materialData) {
    const { title, description } = materialData;
    
    const query = `
      UPDATE learning_materials 
      SET title = $1, description = $2, updated_at = NOW()
      WHERE id = $3
      RETURNING *
    `;
    
    const values = [title, description, id];
    
    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Delete learning material
  static async delete(id) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Delete related records first
      await client.query('DELETE FROM learning_material_students WHERE learning_material_id = $1', [id]);
      await client.query('DELETE FROM practice_exercises WHERE learning_material_id = $1', [id]);
      await client.query('DELETE FROM key_learning_points WHERE learning_material_id = $1', [id]);
      await client.query('DELETE FROM video_lessons WHERE learning_material_id = $1', [id]);
      
      // Delete pre learning sections
      const preLearningResult = await client.query('SELECT id FROM pre_learning WHERE learning_material_id = $1', [id]);
      for (const preLearning of preLearningResult.rows) {
        await client.query('DELETE FROM pre_learning_sections WHERE pre_learning_id = $1', [preLearning.id]);
      }
      await client.query('DELETE FROM pre_learning WHERE learning_material_id = $1', [id]);
      
      // Finally delete the learning material
      const result = await client.query('DELETE FROM learning_materials WHERE id = $1 RETURNING *', [id]);
      
      await client.query('COMMIT');
      return result.rows[0];
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Get learning material with all related data
  static async findByIdWithDetails(id) {
    const client = await pool.connect();
    
    try {
      // Get main learning material
      let materialResult;
      try {
        materialResult = await client.query(`
          SELECT lm.*, 
                 u.first_name || ' ' || u.last_name as created_by_name
          FROM learning_materials lm
          LEFT JOIN users u ON lm.created_by = u.id
          WHERE lm.id = $1
        `, [id]);
      } catch (error) {
        // If created_by column doesn't exist, use fallback query
        if (error.code === '42703') {
          materialResult = await client.query(`
            SELECT *, 'Unknown' as created_by_name
            FROM learning_materials
            WHERE id = $1
          `, [id]);
        } else {
          throw error;
        }
      }
      
      if (materialResult.rows.length === 0) {
        return null;
      }
      
      const material = materialResult.rows[0];
      
      // Get pre learning with complete data (sections and questions)
      const PreLearning = require('./PreLearning');
      material.pre_learning = await PreLearning.findByLearningMaterialIdComplete(id);
      
      // Get video lessons
      const videoResult = await client.query(`
        SELECT * FROM video_lessons WHERE learning_material_id = $1
      `, [id]);
      
      material.video_lessons = videoResult.rows;
      
      // Get key learning points
      const keyPointsResult = await client.query(`
        SELECT * FROM key_learning_points 
        WHERE learning_material_id = $1 
        ORDER BY order_index ASC
      `, [id]);
      
      material.key_learning_points = keyPointsResult.rows;
      
      // Get practice exercises
      const exercisesResult = await client.query(`
        SELECT * FROM practice_exercises 
        WHERE learning_material_id = $1 
        ORDER BY order_index ASC
      `, [id]);
      
      material.practice_exercises = exercisesResult.rows;
      
      // Get assigned students
      const studentsResult = await client.query(`
        SELECT u.id, u.first_name, u.last_name, u.email, lms.assigned_at
        FROM learning_material_students lms
        JOIN users u ON lms.student_id = u.id
        WHERE lms.learning_material_id = $1
        ORDER BY u.first_name, u.last_name
      `, [id]);
      
      material.assigned_students = studentsResult.rows;
      
      return material;
    } catch (error) {
      throw error;
    } finally {
      client.release();
    }
  }

  // Assign students to learning material
  static async assignStudents(materialId, studentIds) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Remove existing assignments
      await client.query('DELETE FROM learning_material_students WHERE learning_material_id = $1', [materialId]);
      
      // Add new assignments
      for (const studentId of studentIds) {
        await client.query(`
          INSERT INTO learning_material_students (learning_material_id, student_id, assigned_at)
          VALUES ($1, $2, NOW())
        `, [materialId, studentId]);
      }
      
      await client.query('COMMIT');
      return true;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Get count of learning materials
  static async getCount(search = null) {
    let query = 'SELECT COUNT(*) FROM learning_materials';
    const values = [];
    
    if (search) {
      query += ' WHERE title ILIKE $1';
      values.push(`%${search}%`);
    }
    
    try {
      const result = await pool.query(query, values);
      return parseInt(result.rows[0].count);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = LearningMaterial;
