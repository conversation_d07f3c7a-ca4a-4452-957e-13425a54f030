const pool = require('../config/database');

class VideoLesson {
  // Create video lesson
  static async create(videoData) {
    const { learning_material_id, title, video_url, duration } = videoData;
    
    const query = `
      INSERT INTO video_lessons (learning_material_id, title, video_url, duration, created_at, updated_at)
      VALUES ($1, $2, $3, $4, NOW(), NOW())
      RETURNING *
    `;
    
    const values = [learning_material_id, title, video_url, duration];
    
    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Find video lessons by learning material ID
  static async findByLearningMaterialId(learningMaterialId) {
    const query = 'SELECT * FROM video_lessons WHERE learning_material_id = $1 ORDER BY created_at ASC';
    
    try {
      const result = await pool.query(query, [learningMaterialId]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }

  // Update video lesson
  static async update(id, videoData) {
    const { title, video_url, duration } = videoData;
    
    const query = `
      UPDATE video_lessons 
      SET title = $1, video_url = $2, duration = $3, updated_at = NOW()
      WHERE id = $4
      RETURNING *
    `;
    
    const values = [title, video_url, duration, id];
    
    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Delete video lesson
  static async delete(id) {
    const query = 'DELETE FROM video_lessons WHERE id = $1 RETURNING *';
    
    try {
      const result = await pool.query(query, [id]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Create or update video lessons for a learning material
  static async createOrUpdateForMaterial(learningMaterialId, videoLessons) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Delete existing video lessons
      await client.query('DELETE FROM video_lessons WHERE learning_material_id = $1', [learningMaterialId]);
      
      const createdLessons = [];
      
      // Create new video lessons
      if (videoLessons && videoLessons.length > 0) {
        for (const lesson of videoLessons) {
          const result = await client.query(`
            INSERT INTO video_lessons (learning_material_id, title, video_url, duration, created_at, updated_at)
            VALUES ($1, $2, $3, $4, NOW(), NOW())
            RETURNING *
          `, [learningMaterialId, lesson.title, lesson.video_url, lesson.duration]);
          
          createdLessons.push(result.rows[0]);
        }
      }
      
      await client.query('COMMIT');
      return createdLessons;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Delete all video lessons for a learning material
  static async deleteByLearningMaterialId(learningMaterialId) {
    const query = 'DELETE FROM video_lessons WHERE learning_material_id = $1 RETURNING *';
    
    try {
      const result = await pool.query(query, [learningMaterialId]);
      return result.rows;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = VideoLesson;
