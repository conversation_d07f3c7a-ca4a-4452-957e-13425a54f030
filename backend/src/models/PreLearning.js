const pool = require('../config/database');

class PreLearning {
  // Create pre learning content
  static async create(preLearningData) {
    const { learning_material_id, title, instructions, total_time_estimate } = preLearningData;
    
    const query = `
      INSERT INTO pre_learning (learning_material_id, title, instructions, total_time_estimate, created_at, updated_at)
      VALUES ($1, $2, $3, $4, NOW(), NOW())
      RETURNING *
    `;
    
    const values = [learning_material_id, title, instructions, total_time_estimate];
    
    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Find pre learning by learning material ID
  static async findByLearningMaterialId(learningMaterialId) {
    const query = 'SELECT * FROM pre_learning WHERE learning_material_id = $1';
    
    try {
      const result = await pool.query(query, [learningMaterialId]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Update pre learning
  static async update(id, preLearningData) {
    const { title, instructions, total_time_estimate } = preLearningData;
    
    const query = `
      UPDATE pre_learning 
      SET title = $1, instructions = $2, total_time_estimate = $3, updated_at = NOW()
      WHERE id = $4
      RETURNING *
    `;
    
    const values = [title, instructions, total_time_estimate, id];
    
    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Delete pre learning
  static async delete(id) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Delete pre learning sections first
      await client.query('DELETE FROM pre_learning_sections WHERE pre_learning_id = $1', [id]);
      
      // Delete pre learning
      const result = await client.query('DELETE FROM pre_learning WHERE id = $1 RETURNING *', [id]);
      
      await client.query('COMMIT');
      return result.rows[0];
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Create or update pre learning with sections and questions
  static async createOrUpdateWithSections(learningMaterialId, preLearningData) {
    const { title, description, total_time_estimate, sections } = preLearningData;
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Check if pre learning already exists
      const existingResult = await client.query(
        'SELECT id FROM pre_learning WHERE learning_material_id = $1',
        [learningMaterialId]
      );
      
      let preLearningId;
      
      if (existingResult.rows.length > 0) {
        // Update existing pre learning
        preLearningId = existingResult.rows[0].id;
        await client.query(`
          UPDATE pre_learning 
          SET title = $1, instructions = $2, total_time_estimate = $3, updated_at = NOW()
          WHERE id = $4
        `, [title, description, total_time_estimate, preLearningId]);
      } else {
        // Create new pre learning
        const createResult = await client.query(`
          INSERT INTO pre_learning (learning_material_id, title, instructions, total_time_estimate, created_at, updated_at)
          VALUES ($1, $2, $3, $4, NOW(), NOW())
          RETURNING id
        `, [learningMaterialId, title, description, total_time_estimate]);
        
        preLearningId = createResult.rows[0].id;
      }
      
      // Delete existing questions first (cascade will handle this, but being explicit)
      await client.query(`
        DELETE FROM pre_learning_questions 
        WHERE section_id IN (
          SELECT id FROM pre_learning_sections WHERE pre_learning_id = $1
        )
      `, [preLearningId]);
      
      // Delete existing sections
      await client.query('DELETE FROM pre_learning_sections WHERE pre_learning_id = $1', [preLearningId]);
      
      // Create new sections with questions
      if (sections && sections.length > 0) {
        for (let i = 0; i < sections.length; i++) {
          const section = sections[i];
          
          // Create section
          const sectionResult = await client.query(`
            INSERT INTO pre_learning_sections (
              pre_learning_id, title, time_estimate, order_index, created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, NOW(), NOW())
            RETURNING id
          `, [
            preLearningId,
            section.title,
            section.time_estimate,
            i + 1
          ]);
          
          const sectionId = sectionResult.rows[0].id;
          
          // Create questions for this section
          if (section.questions && section.questions.length > 0) {
            for (let j = 0; j < section.questions.length; j++) {
              const question = section.questions[j];
              
              await client.query(`
                INSERT INTO pre_learning_questions (
                  section_id, question_type, question, placeholder, max_length,
                  options, number_of_list_items, scale_maximum, scale_label,
                  order_index, created_at, updated_at
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
              `, [
                sectionId,
                question.type,
                question.question,
                question.placeholder || null,
                question.max_length || null,
                question.options ? JSON.stringify(question.options) : null,
                question.number_of_list_items || null,
                question.scale_maximum || null,
                question.scale_label || null,
                j + 1
              ]);
            }
          }
        }
      }
      
      await client.query('COMMIT');
      
      // Return the complete pre learning with sections and questions
      return await this.getCompletePreLearning(preLearningId);
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Get complete pre learning with sections and questions
  static async getCompletePreLearning(preLearningId) {
    const client = await pool.connect();
    
    try {
      // Get pre learning
      const preLearningResult = await client.query(`
        SELECT * FROM pre_learning WHERE id = $1
      `, [preLearningId]);
      
      if (preLearningResult.rows.length === 0) {
        return null;
      }
      
      const preLearning = preLearningResult.rows[0];
      
      // Get sections
      const sectionsResult = await client.query(`
        SELECT * FROM pre_learning_sections 
        WHERE pre_learning_id = $1 
        ORDER BY order_index ASC
      `, [preLearningId]);
      
      // Get questions for each section
      const sections = [];
      for (const section of sectionsResult.rows) {
        const questionsResult = await client.query(`
          SELECT * FROM pre_learning_questions 
          WHERE section_id = $1 
          ORDER BY order_index ASC
        `, [section.id]);
        
        section.questions = questionsResult.rows.map(q => ({
          id: q.id,
          type: q.question_type,
          question: q.question,
          placeholder: q.placeholder,
          max_length: q.max_length,
          options: q.options,
          number_of_list_items: q.number_of_list_items,
          scale_maximum: q.scale_maximum,
          scale_label: q.scale_label,
          order_index: q.order_index
        }));
        
        sections.push(section);
      }
      
      preLearning.sections = sections;
      return preLearning;
    } catch (error) {
      throw error;
    } finally {
      client.release();
    }
  }

  // Find pre learning by learning material ID with complete data
  static async findByLearningMaterialIdComplete(learningMaterialId) {
    try {
      const result = await pool.query(
        'SELECT id FROM pre_learning WHERE learning_material_id = $1',
        [learningMaterialId]
      );
      
      if (result.rows.length === 0) {
        return null;
      }
      
      return await this.getCompletePreLearning(result.rows[0].id);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = PreLearning;
