const express = require('express');
const router = express.Router();
const AdminController = require('../controllers/adminController');
const adminAuth = require('../middleware/adminAuth');

// Apply admin authentication to all routes
router.use(adminAuth);

// Dashboard routes
router.get('/dashboard/stats', AdminController.getDashboardStats);

// Learning materials routes
router.get('/learning-materials', AdminController.getLearningMaterials);
router.get('/learning-materials/:id', AdminController.getLearningMaterial);
router.post('/learning-materials', AdminController.createLearningMaterial);
router.put('/learning-materials/:id', AdminController.updateLearningMaterial);
router.delete('/learning-materials/:id', AdminController.deleteLearningMaterial);
router.post('/learning-materials/:id/duplicate', AdminController.duplicateLearningMaterial);

// Student management routes
router.get('/students', AdminController.getStudents);
router.post('/learning-materials/:id/assign-students', AdminController.assignStudents);

module.exports = router;
