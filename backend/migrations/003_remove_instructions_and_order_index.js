const pool = require('../src/config/database');

async function up() {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    console.log('Removing instructions column from pre_learning table...');
    await client.query('ALTER TABLE pre_learning DROP COLUMN IF EXISTS instructions');
    
    console.log('Removing order_index column from learning_materials table...');
    await client.query('ALTER TABLE learning_materials DROP COLUMN IF EXISTS order_index');
    
    await client.query('COMMIT');
    console.log('Migration 003 completed successfully');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Migration 003 failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function down() {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    console.log('Adding back instructions column to pre_learning table...');
    await client.query('ALTER TABLE pre_learning ADD COLUMN instructions TEXT');
    
    console.log('Adding back order_index column to learning_materials table...');
    await client.query('ALTER TABLE learning_materials ADD COLUMN order_index INTEGER DEFAULT 1');
    
    await client.query('COMMIT');
    console.log('Migration 003 rollback completed successfully');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Migration 003 rollback failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

module.exports = { up, down };
