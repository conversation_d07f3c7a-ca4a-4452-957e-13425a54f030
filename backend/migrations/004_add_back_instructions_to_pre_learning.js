const pool = require('../src/config/database');

async function up() {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    console.log('Adding back instructions column to pre_learning table...');
    await client.query('ALTER TABLE pre_learning ADD COLUMN IF NOT EXISTS instructions TEXT');
    
    await client.query('COMMIT');
    console.log('Migration 004 completed successfully');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Migration 004 failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function down() {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    console.log('Removing instructions column from pre_learning table...');
    await client.query('ALTER TABLE pre_learning DROP COLUMN IF EXISTS instructions');
    
    await client.query('COMMIT');
    console.log('Migration 004 rollback completed successfully');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Migration 004 rollback failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

module.exports = { up, down };
