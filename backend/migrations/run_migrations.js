const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
  ssl: {
    rejectUnauthorized: false
  }
});

async function createMigrationsTable() {
  const client = await pool.connect();
  try {
    await client.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        filename VARCHAR(255) UNIQUE NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Migrations table created/verified');
  } catch (error) {
    console.error('❌ Error creating migrations table:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function getExecutedMigrations() {
  const client = await pool.connect();
  try {
    const result = await client.query('SELECT filename FROM migrations ORDER BY id');
    return result.rows.map(row => row.filename);
  } catch (error) {
    console.error('❌ Error getting executed migrations:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function markMigrationAsExecuted(filename) {
  const client = await pool.connect();
  try {
    await client.query('INSERT INTO migrations (filename) VALUES ($1)', [filename]);
    console.log(`✅ Marked migration as executed: ${filename}`);
  } catch (error) {
    console.error(`❌ Error marking migration as executed: ${filename}`, error);
    throw error;
  } finally {
    client.release();
  }
}

async function runMigrations() {
  try {
    console.log('🚀 Starting migration process...');
    
    // Create migrations table if it doesn't exist
    await createMigrationsTable();
    
    // Get list of executed migrations
    const executedMigrations = await getExecutedMigrations();
    console.log('📋 Executed migrations:', executedMigrations);
    
    // Get all migration files
    const migrationsDir = __dirname;
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.js') && file !== 'run_migrations.js')
      .sort();
    
    console.log('📁 Available migration files:', migrationFiles);
    
    // Run pending migrations
    for (const filename of migrationFiles) {
      if (!executedMigrations.includes(filename)) {
        console.log(`🔄 Running migration: ${filename}`);
        
        const migrationPath = path.join(migrationsDir, filename);
        const migration = require(migrationPath);
        
        if (migration.up) {
          await migration.up();
          await markMigrationAsExecuted(filename);
          console.log(`✅ Migration completed: ${filename}`);
        } else {
          console.log(`⚠️  Migration file ${filename} does not export up function`);
        }
      } else {
        console.log(`⏭️  Skipping already executed migration: ${filename}`);
      }
    }
    
    console.log('🎉 All migrations completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration process failed:', error);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  runMigrations()
    .then(() => {
      console.log('Migration runner completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration runner failed:', error);
      process.exit(1);
    });
}

module.exports = { runMigrations };
