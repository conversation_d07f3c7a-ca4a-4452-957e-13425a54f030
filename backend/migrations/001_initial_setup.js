const pool = require('../src/config/database');
const fs = require('fs');
const path = require('path');

async function up() {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    console.log('Setting up initial database tables...');
    
    // Read the schema file
    const schemaPath = path.join(__dirname, '..', 'database_schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Execute the schema
    await client.query(schema);
    
    await client.query('COMMIT');
    console.log('Migration 001 completed successfully');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Migration 001 failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function down() {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    console.log('Dropping all tables...');
    
    // Drop tables in reverse order of dependencies
    const dropQueries = [
      'DROP TABLE IF EXISTS learning_material_students CASCADE',
      'DROP TABLE IF EXISTS practice_exercises CASCADE',
      'DROP TABLE IF EXISTS key_learning_points CASCADE',
      'DROP TABLE IF EXISTS video_lessons CASCADE',
      'DROP TABLE IF EXISTS pre_learning_questions CASCADE',
      'DROP TABLE IF EXISTS pre_learning_sections CASCADE',
      'DROP TABLE IF EXISTS pre_learning CASCADE',
      'DROP TABLE IF EXISTS learning_materials CASCADE',
      'DROP TABLE IF EXISTS users CASCADE'
    ];
    
    for (const query of dropQueries) {
      await client.query(query);
    }
    
    await client.query('COMMIT');
    console.log('Migration 001 rollback completed successfully');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Migration 001 rollback failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

module.exports = { up, down };
