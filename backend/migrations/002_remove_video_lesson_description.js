const pool = require('../src/config/database');

async function up() {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    console.log('Removing description column from video_lessons table...');
    
    // Check if description column exists before trying to drop it
    const checkColumn = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'video_lessons' 
      AND column_name = 'description'
    `);
    
    if (checkColumn.rows.length > 0) {
      await client.query('ALTER TABLE video_lessons DROP COLUMN IF EXISTS description');
      console.log('Successfully removed description column from video_lessons table');
    } else {
      console.log('Description column does not exist in video_lessons table, skipping...');
    }
    
    await client.query('COMMIT');
    console.log('Migration 002 completed successfully');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Migration 002 failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function down() {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    console.log('Adding back description column to video_lessons table...');
    await client.query('ALTER TABLE video_lessons ADD COLUMN description TEXT');
    
    await client.query('COMMIT');
    console.log('Migration 002 rollback completed successfully');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Migration 002 rollback failed:', error);
    throw error;
  } finally {
    client.release();
  }
}

module.exports = { up, down };
