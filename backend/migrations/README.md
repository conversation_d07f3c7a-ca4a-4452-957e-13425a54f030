# Database Migrations

This directory contains database migration scripts for the PokerCoach Pro application.

## Migration System

The migration system tracks which migrations have been executed and ensures they are only run once. It uses a `migrations` table to keep track of executed migrations.

## Files

- `001_initial_setup.js` - Initial database setup (moved from setup_database.js)
- `002_remove_video_lesson_description.js` - Removes description field from video_lessons table
- `run_migrations.js` - Migration runner that executes all pending migrations

## Running Migrations

### Run All Pending Migrations
```bash
cd backend
npm run migrate
```

### Run Initial Setup Only
```bash
cd backend
npm run setup-db
```

### Run Specific Migration
```bash
cd backend
node migrations/002_remove_video_lesson_description.js
```

## Creating New Migrations

1. Create a new file with the naming convention: `XXX_description.js` where XXX is the next sequential number
2. Follow this template:

```javascript
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('Starting migration: Your migration description...');
    
    // Your migration SQL here
    await client.query('YOUR SQL STATEMENT');
    
    console.log('✅ Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  runMigration()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { runMigration };
```

## Migration History

- `001_initial_setup.js` - Creates all initial tables and indexes
- `002_remove_video_lesson_description.js` - Removes description column from video_lessons table

## Notes

- Migrations are executed in alphabetical order
- Each migration is only executed once
- The migration runner creates a `migrations` table to track execution
- Always test migrations on a development database first
- Migrations should be idempotent when possible (safe to run multiple times)
