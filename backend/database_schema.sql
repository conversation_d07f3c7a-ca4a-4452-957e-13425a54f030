-- Database schema for PokerCoach Pro
-- This file contains the complete database structure

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    first_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'student',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Learning materials table
CREATE TABLE IF NOT EXISTS learning_materials (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Pre learning table
CREATE TABLE IF NOT EXISTS pre_learning (
    id SERIAL PRIMARY KEY,
    learning_material_id INTEGER REFERENCES learning_materials(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    instructions TEXT,
    total_time_estimate VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Pre learning sections table
CREATE TABLE IF NOT EXISTS pre_learning_sections (
    id SERIAL PRIMARY KEY,
    pre_learning_id INTEGER REFERENCES pre_learning(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    time_estimate VARCHAR(50),
    order_index INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Pre learning questions table
CREATE TABLE IF NOT EXISTS pre_learning_questions (
    id SERIAL PRIMARY KEY,
    section_id INTEGER REFERENCES pre_learning_sections(id) ON DELETE CASCADE,
    question_type VARCHAR(50) NOT NULL, -- 'Prediction', 'Text Input', 'Long Text', 'Multiple Choice', 'List', 'Scale Rating'
    question TEXT NOT NULL,
    placeholder TEXT,
    max_length INTEGER, -- For Long Text
    options JSONB, -- For Multiple Choice (array of options)
    number_of_list_items INTEGER, -- For List
    scale_maximum INTEGER, -- For Scale Rating
    scale_label VARCHAR(255), -- For Scale Rating
    order_index INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Video lessons table
CREATE TABLE IF NOT EXISTS video_lessons (
    id SERIAL PRIMARY KEY,
    learning_material_id INTEGER REFERENCES learning_materials(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    video_url VARCHAR(500) NOT NULL,
    duration VARCHAR(50),
    order_index INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Key learning points table
CREATE TABLE IF NOT EXISTS key_learning_points (
    id SERIAL PRIMARY KEY,
    learning_material_id INTEGER REFERENCES learning_materials(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    order_index INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Practice exercises table
CREATE TABLE IF NOT EXISTS practice_exercises (
    id SERIAL PRIMARY KEY,
    learning_material_id INTEGER REFERENCES learning_materials(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    instructions TEXT,
    order_index INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Learning material students assignment table
CREATE TABLE IF NOT EXISTS learning_material_students (
    id SERIAL PRIMARY KEY,
    learning_material_id INTEGER REFERENCES learning_materials(id) ON DELETE CASCADE,
    student_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(learning_material_id, student_id)
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_pre_learning_material_id ON pre_learning(learning_material_id);
CREATE INDEX IF NOT EXISTS idx_pre_learning_sections_pre_learning_id ON pre_learning_sections(pre_learning_id);
CREATE INDEX IF NOT EXISTS idx_pre_learning_questions_section_id ON pre_learning_questions(section_id);
CREATE INDEX IF NOT EXISTS idx_video_lessons_material_id ON video_lessons(learning_material_id);
CREATE INDEX IF NOT EXISTS idx_key_learning_points_material_id ON key_learning_points(learning_material_id);
CREATE INDEX IF NOT EXISTS idx_practice_exercises_material_id ON practice_exercises(learning_material_id);
CREATE INDEX IF NOT EXISTS idx_learning_material_students_material_id ON learning_material_students(learning_material_id);
CREATE INDEX IF NOT EXISTS idx_learning_material_students_student_id ON learning_material_students(student_id);
